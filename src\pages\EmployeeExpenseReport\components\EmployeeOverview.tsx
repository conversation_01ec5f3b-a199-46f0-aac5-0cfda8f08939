const employeeData = [
  { label: 'Name', value: '<PERSON>' },
  { label: 'Role', value: 'Product Manager' },
  { label: 'Department', value: 'Operations' },
  { label: 'Location', value: 'New York' },
];

const summaryData = [
  { label: 'Total Expenses', value: '$4320.00' },
  { label: '# of Claims', value: '12' },
  { label: 'Avg. Approval Time', value: '1.6 days' },
  { label: '# of Disputes', value: '1' },
];

const reportData = [{ label: 'Period', value: 'Jan 1 - Jan 31, 2025' }];

const DetailMapper = ({
  data,
  title,
}: {
  data: { label: string; value: string }[];
  title: string;
}) => {
  return (
    <div className="mb-6 w-full">
      <h2 className="text-base font-bold text-gray-500 mb-8">{title}</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-5">
        {data.map((item, index) => (
          <div key={index} className="flex flex-col border-t border-gray-200 pt-4">
            <span className="text-xs font-semibold text-gray-500">{item.label}</span>
            <span className="text-sm font-semibold text-gray-700 mt-1">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

const EmployeeOverview = () => {
  return (
    <div className="p-8 rounded-lg bg-gray-50">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-24">
        <DetailMapper title="Employee Details" data={employeeData} />
        <DetailMapper title="Key Summary" data={summaryData} />
        <DetailMapper title="Report Period" data={reportData} />
      </div>
    </div>
  );
};

export default EmployeeOverview;
