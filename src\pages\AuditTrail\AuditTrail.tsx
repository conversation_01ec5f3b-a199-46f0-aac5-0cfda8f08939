import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { FiSearch } from 'react-icons/fi';
import Dropdown from '../../components/Dropdown';
import Pagination from '../../components/Pagination';

interface AuditLogEntry {
  timestamp: string;
  timeDetail: string;
  user: string;
  actionType: string;
  objectItem: string;
  description: string;
  ipAddress: string;
  status: 'Success' | 'Failure';
}

const itemsPerPage = 4;

  const auditLogs: AuditLogEntry[] = [
    {
      timestamp: '07/26/2024',
      timeDetail: '14:30:00',
      user: '<PERSON>',
      actionType: 'User Login',
      objectItem: 'N/A',
      description: 'Successful login',
      ipAddress: '*************',
      status: 'Success',
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '14:45:15',
      user: '<PERSON>',
      actionType: 'Report Generated',
      objectItem: 'Report #12345',
      description: 'Generated sales report for Q2',
      ipAddress: '*************',
      status: 'Success',
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:00:20',
      user: '<PERSON>',
      actionType: 'User Logout',
      objectItem: 'N/A',
      description: 'User logged out',
      ipAddress: '*************',
      status: 'Success',
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:15:30',
      user: 'Liam Carter',
      actionType: 'Data Update',
      objectItem: 'Customer #67890',
      description: 'Changed: Max Amount from $50 to $60',
      ipAddress: '*************',
      status: 'Success',
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:30:45',
      user: 'Sophia Evans',
      actionType: 'System Error',
      objectItem: 'N/A',
      description: 'Failed to process payment transaction',
      ipAddress: '*************',
      status: 'Failure',
    },
  ];

const AuditTrail = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [actionType, setActionType] = useState('');
  const [department, setDepartment] = useState('');
  const [role, setRole] = useState('');
  const [approvalStatus, setApprovalStatus] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const filteredLogs = auditLogs.filter(
    (log) =>
      log.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.actionType.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);
  const currentLogs = filteredLogs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Audit Trail</h1>
          <Breadcrumb pageName="Audit Trail" />
        </div>
      </div>

      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FiSearch className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="bg-gray-100 border border-gray-200 text-gray-700 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full pl-10 p-2.5"
          placeholder="Search audit logs..."
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setCurrentPage(1);
          }}
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Dropdown
          label="Action/Event Type"
          value={actionType}
          onChange={setActionType}
          options={['User Login', 'User Logout', 'Report Generated', 'Data Update', 'System Error']}
          placeholder="Action/Event Type"
          fullWidth
        />
        <Dropdown
          label="Department"
          value={department}
          onChange={setDepartment}
          options={['Finance', 'HR', 'IT', 'Sales']}
          placeholder="Department"
          fullWidth
        />
        <Dropdown
          label="Role"
          value={role}
          onChange={setRole}
          options={['Admin', 'Manager', 'User']}
          placeholder="Role"
          fullWidth
        />
        <Dropdown
          label="Approval Status"
          value={approvalStatus}
          onChange={setApprovalStatus}
          options={['Approved', 'Pending', 'Rejected']}
          placeholder="Approval Status"
          fullWidth
        />
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[720px] mt-4">
            <thead>
              <tr className="border-b border-gray-200 text-left text-sm text-gray-800">
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Timestamp</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">User</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Action/Event Type</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Object/item</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Details/Description</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">IP Address</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Status</th>
              </tr>
            </thead>
            <tbody>
              {currentLogs.map((log, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 text-sm">
                  <td className="py-4 px-2 sm:px-4">
                    <div className="text-sm font-semibold text-gray-700">{log.timestamp}</div>
                    <div className="text-xs text-gray-500">{log.timeDetail}</div>
                  </td>
                  <td className="py-4 px-2 sm:px-4 font-medium text-gray-700">{log.user}</td>
                  <td className="py-4 px-2 sm:px-4 text-gray-600">{log.actionType}</td>
                  <td className="py-4 px-2 sm:px-4 text-gray-600">{log.objectItem}</td>
                  <td className="py-4 px-2 sm:px-4 text-gray-600">{log.description}</td>
                  <td className="py-4 px-2 sm:px-4 text-gray-600">{log.ipAddress}</td>
                  <td className="py-4 px-2 sm:px-4">
                    <span
                      className={`px-2.5 py-1 text-xs font-semibold rounded-full text-gray-700 bg-gray-100`}
                    >
                      {log.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-100">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default AuditTrail;
