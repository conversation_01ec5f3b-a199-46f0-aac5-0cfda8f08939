const disputesData = [
  {
    date: 'Jan 15 2025',
    expense: 'Client Dinner',
    amount: 120.0,
    disputeStatus: 'Under Review',
    reasonSummary: 'Missing itemized receipt',
  },
];

const DisputeExceptionTable = () => {
  return (
    <div>
      <h2 className="text-lg font-bold text-gray-500 mb-6">Disputes & Exceptions</h2>

      <div className="overflow-x-auto rounded-xl border border-gray-200">
        <table className="w-full">
          <thead className="bg-white">
            <tr className="border-b border-gray-200 bg-gray-50">
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Date
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Expense
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Amount ($)
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Dispute Status
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Reason Summary
              </th>
            </tr>
          </thead>
          <tbody>
            {disputesData.length > 0 ? (
              disputesData.map((dispute, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-200 text-gray-500 font-semibold text-nowrap"
                >
                  <td className="py-4 px-6 text-sm">{dispute.date}</td>
                  <td className="py-4 px-6 text-sm">{dispute.expense}</td>
                  <td className="py-4 px-6 text-sm">{dispute.amount.toFixed(2)}</td>
                  <td className="py-4 px-6 text-sm">{dispute.disputeStatus}</td>
                  <td className="py-4 px-6 text-sm">{dispute.reasonSummary}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="py-8 px-6 text-center text-sm text-gray-500">
                  No disputes or exceptions found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DisputeExceptionTable;
