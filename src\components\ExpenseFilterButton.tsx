import { useState } from 'react';
import { Filter } from 'lucide-react';
import ExpenseFilters, { type ExpenseFilterValues } from './ExpenseFilters';

interface ExpenseFilterButtonProps {
  onApplyFilters: (filters: ExpenseFilterValues) => void;
}

export default function ExpenseFilterButton({ onApplyFilters }: ExpenseFilterButtonProps) {
  const [showFilters, setShowFilters] = useState(false);

  const handleApplyFilters = (filters: ExpenseFilterValues) => {
    onApplyFilters(filters);
    setShowFilters(false);
  };

  const handleReset = () => {
    setShowFilters(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setShowFilters(!showFilters)}
        className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
      >
        <Filter className="w-4 h-4" />
        <span>Filters</span>
      </button>

      {showFilters && (
        <div className="absolute right-0 mt-2 z-30 w-[calc(100vw-2rem)] max-w-4xl shadow-lg">
          <ExpenseFilters onApplyFilters={handleApplyFilters} onReset={handleReset} />
        </div>
      )}
    </div>
  );
}
