export interface Category {
  id: string;
  icon: string;
  iconBg: string;
  name: string;
  description: string;
  status: 'Active' | 'Inactive';
}

export const categoriesData: Category[] = [
  {
    id: '1',
    icon: '/planeIcon.svg',
    iconBg: 'bg-teal-600',
    name: 'Travel & Transportation',
    description:
      'Covers travel costs for business trips, including transportation, accommodation, and meals.',
    status: 'Active',
  },
  {
    id: '2',
    icon: '/burgerCategoryIcon.svg',
    iconBg: 'bg-teal-600',
    name: 'Meals & Entertainment',
    description: 'Reimbursement for meals during business travel or client meetings.',
    status: 'Active',
  },
  {
    id: '3',
    icon: '/penIcon.svg',
    iconBg: 'bg-teal-600',
    name: 'Office Supplies',
    description: 'Covers the purchase of necessary office supplies.',
    status: 'Active',
  },
  {
    id: '4',
    icon: '/certificateIcon.svg',
    iconBg: 'bg-teal-600',
    name: 'Training & Professional Services',
    description: 'Reimbursement for professional development courses and training programs.',
    status: 'Active',
  },
  {
    id: '5',
    icon: '/miscIcon.svg',
    iconBg: 'bg-teal-600',
    name: 'Miscellaneous',
    description: 'Petty cash, office refreshments, other ad-hoc expenses',
    status: 'Active',
  },
  {
    id: '6',
    icon: '/lodgingIcon.svg',
    iconBg: 'bg-teal-600',
    name: 'Lodging',
    description: 'Hotels, serviced apartments, conference accommodations',
    status: 'Inactive',
  },
];
