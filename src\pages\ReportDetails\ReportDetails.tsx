import SingleExpenseTable from './components/SingleExpenseTable';
import ActivityInsightsFeed from './components/ActivityInsightsFeed';
import AnomaliesSection from './components/AnomaliesSection';
import SingleActiveDisputesTable from './components/SingleActiveDisputesTable';
import { sampleDisputes } from '../../mockData/mockData';
import SinglePastExpensesTable from './components/SinglePastExpensesTable';
import EmployeeSummaryCard from './components/EmployeeSummaryCard';
import ReportDetailsHeader from './components/ReportDetailsHeader';
import { singleExpenseTableData } from '../../mockData/expenseReportData';

const ReportDetails = () => {
  return (
    <div className="p-4 sm:p-6 bg-gray-50">
      <ReportDetailsHeader />

      <EmployeeSummaryCard />

      <SingleExpenseTable title="Latest Expense Requests" expenses={singleExpenseTableData} />

      <div className="bg-white p-6 rounded-lg shadow-sm mt-6">
        <h2 className="text-lg font-semibold text-gray-600 mb-6">Activity & Insights</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-stretch">
          <ActivityInsightsFeed />
          <AnomaliesSection />
        </div>
      </div>

      <SingleActiveDisputesTable title="Active Disputes" disputes={sampleDisputes} />

      <SinglePastExpensesTable />
    </div>
  );
};

export default ReportDetails;
