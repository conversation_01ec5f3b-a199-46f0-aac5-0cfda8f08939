import api from "../lib/axiosInstance";

export const downloadUserTemplate = async (): Promise<Blob> => {
  const response = await api.get("/api/core/users/download_template/", {
    responseType: 'blob',
  });
  return response.data;
};

export const bulkUploadUsers = async (file: File): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await api.post("/api/core/users/bulk_upload/", formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};