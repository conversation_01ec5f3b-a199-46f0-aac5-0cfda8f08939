import api from '../lib/axiosInstance';

export interface User {
  id: number;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone: string;
  company: number;
  company_name: string;
  department: number | null;
  department_name?: string;
  groups: string[];
  manager: number | null;
  auth_source: string;
  auth_source_display: string;
  is_active: boolean;
  date_joined: string;
  last_login: string | null;
  expense_limit: number | null;
  can_approve_expenses: boolean;
}

export interface UsersResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: User[];
}

export const downloadUserTemplate = async (): Promise<Blob> => {
  const response = await api.get('/api/core/users/download_template/', {
    responseType: 'blob',
  });
  return response.data;
};

export const bulkUploadUsers = async (file: File): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await api.post('/api/core/users/bulk_upload/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const fetchUsers = async (): Promise<UsersResponse> => {
  const response = await api.get('/api/core/users/');
  return response.data;
};
