import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Mail } from 'lucide-react';

import Dropdown from '../../../components/Dropdown';
import Pagination from '../../../components/Pagination';
import DatePickerWithLabel from '../../../components/DatePicker';
import EmptyState from '../../../components/EmptyState';
import { fetchUsers, type User } from '../../../api/userManagementApis';

interface UserTableProps {
  title: string;
}

interface UserTableRef {
  refreshUsers: () => void;
}

const ITEMS_PER_PAGE = 6;

const UserTable = forwardRef<UserTableRef, UserTableProps>(({ title }, ref) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [roleFilter, setRoleFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetchUsers();
      setUsers(response.results);
    } catch (err) {
      setError('Failed to load users. Please try again.');
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    refreshUsers: loadUsers,
  }));

  const filteredUsers = users.filter((user) => {
    const userRole = user.groups.length > 0 ? user.groups[0] : '';
    const userDepartment = user.department_name || '';
    const userStatus = user.is_active ? 'Active' : 'Inactive';

    return (
      (roleFilter === '' || roleFilter === 'All' || userRole === roleFilter) &&
      (departmentFilter === '' ||
        departmentFilter === 'All' ||
        userDepartment === departmentFilter) &&
      (statusFilter === '' || statusFilter === 'All' || userStatus === statusFilter)
    );
  });

  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(filteredUsers.length / ITEMS_PER_PAGE);

  // Get unique values for filters
  const uniqueRoles = Array.from(new Set(users.flatMap((user) => user.groups))).filter(Boolean);
  const uniqueDepartments = Array.from(
    new Set(users.map((user) => user.department_name).filter(Boolean))
  ) as string[];

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm mt-6">
        <div className="p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-6">{title}</h3>
          <div className="flex items-center justify-center py-12">
            <div className="text-slate-600">Loading users...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm mt-6">
        <div className="p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-6">{title}</h3>
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
            {error}
            <button onClick={loadUsers} className="ml-2 text-red-500 hover:text-red-700 underline">
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (users.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm mt-6">
        <div className="p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-6">{title}</h3>
          <EmptyState
            title="No Users Found"
            description="There are no users in the system yet. Add users to get started."
            icon="users"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm mt-6">
      <div className="p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 sm:mb-6 gap-4">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900">{title}</h3>

          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-2">
              <DatePickerWithLabel
                selected={selectedDate}
                onChange={(date) => setSelectedDate(date)}
                placeholderText="Date"
                className="text-sm text-gray-700 w-full outline-none"
                dateFormat="dd/MM/yyyy"
              />
            </div>

            <div className="flex flex-wrap items-center gap-2">
              <Dropdown
                label="Role"
                value={roleFilter}
                onChange={setRoleFilter}
                options={['All', ...uniqueRoles]}
              />
              <Dropdown
                label="Department"
                value={departmentFilter}
                onChange={setDepartmentFilter}
                options={['All', ...uniqueDepartments]}
              />
              <Dropdown
                label="Status"
                value={statusFilter}
                onChange={setStatusFilter}
                options={['All', 'Active', 'Inactive']}
              />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full min-w-[640px] mt-8">
            <thead>
              <tr className="border-b border-gray-200">
                {['Name', 'Email', 'Department', 'Role', 'Status', 'Last Login', 'Actions'].map(
                  (label) => {
                    const hiddenClass =
                      label === 'Email'
                        ? 'hidden sm:table-cell'
                        : label === 'Last Login'
                        ? 'hidden lg:table-cell'
                        : '';
                    return (
                      <th
                        key={label}
                        className={`text-left py-3 px-2 sm:px-4 font-medium text-gray-700 text-xs sm:text-sm ${hiddenClass}`}
                      >
                        {label}
                      </th>
                    );
                  }
                )}
              </tr>
            </thead>
            <tbody>
              {paginatedUsers.map((user) => {
                const userRole = user.groups.length > 0 ? user.groups[0] : 'No Role';
                const userDepartment = user.department_name || 'No Department';
                const userStatus = user.is_active ? 'Active' : 'Inactive';
                const lastLoginDate = user.last_login
                  ? new Date(user.last_login).toLocaleDateString()
                  : 'Never';

                return (
                  <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-2 sm:px-4 align-middle text-nowrap">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <img
                          src="/avatarImage.png"
                          alt="avatar"
                          className="relative inline-block h-6 w-6 !rounded-full object-cover object-center"
                        />
                        <div className="min-w-0">
                          <div className="font-medium text-gray-900 text-xs sm:text-sm truncate">
                            {user.full_name}
                          </div>
                          <div className="text-gray-600 text-xs sm:hidden truncate">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </td>

                    <td className="py-3 text-nowrap px-2 sm:px-4 text-gray-600 text-xs sm:text-sm hidden sm:table-cell align-middle">
                      <div className="flex items-center gap-2 text-sm text-gray-700">
                        <Mail className="w-4 h-4 text-teal-800" />
                        <span>{user.email}</span>
                      </div>
                    </td>

                    <td className="py-3 px-2 text-nowrap sm:px-4 text-gray-600 text-xs sm:text-sm align-middle">
                      {userDepartment}
                    </td>

                    <td className="py-3 px-2 sm:px-4 text-gray-800 text-xs sm:text-sm align-middle">
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100">
                        {userRole}
                      </span>
                    </td>

                    <td className="py-3 text-nowrap px-2 sm:px-4 align-middle">
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          userStatus === 'Active'
                            ? 'bg-teal-100 text-teal-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {userStatus}
                      </span>
                    </td>

                    <td className="py-3 text-nowrap px-2 sm:px-4 text-gray-600 text-xs font-semibold align-middle hidden lg:table-cell">
                      {lastLoginDate}
                    </td>

                    <td className="py-3 px-2 sm:px-4 align-middle font-semibold">
                      <div className="flex items-center gap-1 sm:gap-2">
                        <button
                          className="text-teal-600 hover:text-teal-800 text-xs sm:text-sm cursor-pointer"
                          onClick={() => alert('View user')}
                        >
                          view
                        </button>
                        <span className="text-gray-300">/</span>
                        <button
                          onClick={() => alert('Edit user')}
                          className="text-teal-600 hover:text-teal-800 text-xs sm:text-sm cursor-pointer"
                        >
                          edit
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </div>
  );
});

UserTable.displayName = 'UserTable';

export default UserTable;
