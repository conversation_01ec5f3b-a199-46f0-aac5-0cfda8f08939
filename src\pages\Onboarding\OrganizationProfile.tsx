import { ChevronDown, Calendar } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const OrganizationProfile = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen">
      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full" />
                </div>

                <div className="w-50 h-10">
                  <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full" />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em] ">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">Setup Progress</p>
              <p className="text-xs text-gray-500">1 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step === 1 ? 'bg-[#06B217]' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <div className="w-5 h-5 bg-white rounded-full" />
          </div>
          <h1 className="text-3xl font-semibold text-gray-600">Organization Profile</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="bg-white rounded-lg p-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="space-y-8">
              <FormGroup label="Company Name" placeholder="Enter company name" />
              <FormGroup
                label="Company Address"
                placeholder="Enter company address"
                type="textarea"
              />
              <FormGroup label="City" placeholder="Enter city" />
              <FormGroup label="State/Province" placeholder="Enter state/province" />
              <FormGroup label="Postal Code" placeholder="Enter postal code" />
            </div>

            <div className="space-y-8">
              <FormGroup label="Country" placeholder="Enter country" />
              <FormGroup label="Contact Email" placeholder="Enter contact email" type="email" />
              <FormGroup label="Contact Phone Number" placeholder="Enter contact phone number" />

              <div>
                <label className="block text-base font-medium text-gray-800 mb-3">Industry</label>
                <div className="relative">
                  <select className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors appearance-none bg-white text-gray-500 text-base">
                    <option>Select industry</option>
                    <option>Technology</option>
                    <option>Healthcare</option>
                    <option>Finance</option>
                    <option>Education</option>
                    <option>Manufacturing</option>
                    <option>Retail</option>
                    <option>Other</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                </div>
              </div>

              <div>
                <label className="block text-base font-medium text-gray-800 mb-3">
                  Fiscal Year Start Date
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Select date"
                    className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 pr-12 text-base"
                  />
                  <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                </div>
                <div className="flex justify-end pt-10">
                  <button
                    type="button"
                    className="px-10 py-4 bg-teal-500 text-white font-medium rounded-xl hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base"
                    onClick={() => navigate('/onboarding/add-employee')}
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default OrganizationProfile;

const FormGroup = ({
  label,
  placeholder,
  type = 'text',
}: {
  label: string;
  placeholder: string;
  type?: 'text' | 'email' | 'textarea';
}) => {
  return (
    <div>
      <label className="block text-base font-medium text-gray-800 mb-3">{label}</label>
      {type === 'textarea' ? (
        <textarea
          rows={5}
          placeholder={placeholder}
          className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 resize-none text-base"
        />
      ) : (
        <input
          type={type}
          placeholder={placeholder}
          className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
        />
      )}
    </div>
  );
};
