import React, { useState } from 'react';
import Pagination from '../../../components/Pagination';
import { recentActivity } from '../../../mockData/homeMockData';

const ITEMS_PER_PAGE = 5;

const ActivityList: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(recentActivity.length / ITEMS_PER_PAGE);

  const paginatedActivities = recentActivity.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className="bg-white rounded-lg shadow-sm mt-6">
      <div className="p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 gap-4">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full min-w-[640px] mt-4">
            <thead>
              <tr className="border-b border-gray-200 text-left text-sm text-gray-800">
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Activity</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Details</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Date</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedActivities.map((activity) => (
                <tr key={activity.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-2 sm:px-4 text-sm font-medium text-gray-600">
                    {activity.title}
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">
                    Submitted by {activity.submittedBy}
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-xs font-semibold text-gray-600">
                    {activity.date}
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <button
                      onClick={() => alert('View activity')}
                      className="text-xs text-nowrap font-bold text-teal-500 hover:text-teal-800 cursor-pointer"
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>
    </div>
  );
};

export default ActivityList;
