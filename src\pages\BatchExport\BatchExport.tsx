import React, { useState, useCallback, useMemo } from 'react';
import { Check, FileText } from 'lucide-react';
import DatePicker from '../../components/DatePicker';
import Dropdown from '../../components/Dropdown';

interface ExpenseItem {
  id: string;
  name: string;
  role: string;
  department: string;
  expenseType: string;
  submissionDate: string;
  amount: number;
  status: 'Pending' | 'Approved' | 'In review';
  selected: boolean;
}

const BatchExport: React.FC = () => {
  const [expenses, setExpenses] = useState<ExpenseItem[]>([
    {
      id: '1',
      name: '<PERSON>',
      role: 'Sales Executive',
      department: 'Marketing',
      expenseType: 'Multiple Expenses',
      submissionDate: '2024-07-25',
      amount: 180.0,
      status: 'Pending',
      selected: true,
    },
    {
      id: '2',
      name: '<PERSON>',
      role: 'Marketing Executive',
      department: 'Marketing',
      expenseType: 'Conference Expenses',
      submissionDate: '2024-06-12',
      amount: 250.0,
      status: 'Approved',
      selected: true,
    },
    {
      id: '3',
      name: '<PERSON>',
      role: 'Marketing Executive',
      department: 'Marketing',
      expenseType: 'Software Licenses',
      submissionDate: '2024-08-05',
      amount: 1200.0,
      status: 'Pending',
      selected: true,
    },
    {
      id: '4',
      name: 'Ava Thompson',
      role: 'Marketing Executive',
      department: 'Marketing',
      expenseType: 'Recruitment Costs',
      submissionDate: '2024-09-15',
      amount: 900.0,
      status: 'In review',
      selected: true,
    },
    {
      id: '5',
      name: 'Sofia Martinez',
      role: 'Marketing Executive',
      department: 'Marketing',
      expenseType: 'Conference Expenses',
      submissionDate: '2024-06-12',
      amount: 250.0,
      status: 'Approved',
      selected: true,
    },
    {
      id: '6',
      name: 'Sofia Martinez',
      role: 'Marketing Executive',
      department: 'Marketing',
      expenseType: 'Conference Expenses',
      submissionDate: '2024-06-12',
      amount: 250.0,
      status: 'Approved',
      selected: true,
    },
  ]);

  const [selectAll, setSelectAll] = useState(true);

  const toggleExpenseSelection = useCallback((id: string) => {
    setExpenses((prev) =>
      prev.map((expense) =>
        expense.id === id ? { ...expense, selected: !expense.selected } : expense
      )
    );
  }, []);

  const toggleSelectAll = useCallback(() => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    setExpenses((prev) => prev.map((expense) => ({ ...expense, selected: newSelectAll })));
  }, [selectAll]);

  const selectedCount = useMemo(
    () => expenses.filter((expense) => expense.selected).length,
    [expenses]
  );

  const handleExport = useCallback(() => {
    const selectedExpenses = expenses.filter((expense) => expense.selected);
    console.log('Exporting expenses:', selectedExpenses);
  }, [expenses]);

  const getStatusBadge = (status: string) => {
    const baseClasses = 'px-3 py-1 rounded-full text-sm font-medium transition-all duration-200';
    switch (status) {
      case 'Approved':
        return `${baseClasses} bg-green-100 text-green-700`;
      case 'Pending':
        return `${baseClasses} bg-gray-100 text-gray-700`;
      case 'In review':
        return `${baseClasses} bg-blue-100 text-blue-700`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-700`;
    }
  };

  const formatAmount = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  return (
    <div className="bg-gray-50 p-4 lg:p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-2xl lg:text-3xl font-semibold text-gray-800 mb-2">
                Batch Export Expenses
              </h1>
              <nav className="text-sm text-gray-500">
                <span>Dashboard</span>
                <span className="mx-2">/</span>
                <span>Expense Reports</span>
                <span className="mx-2">/</span>
                <span className="text-teal-600">Batch Export</span>
              </nav>
            </div>

            <div className="flex items-center gap-3">
              <div className="bg-teal-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                {selectedCount} Reports selected from Department - Marketing, Role - All
              </div>
              <button
                onClick={handleExport}
                className="bg-teal-500 hover:bg-teal-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 whitespace-nowrap"
              >
                Export Selected
              </button>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-800 mb-6">Raised Disputes</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="relative w-full">
                <DatePicker
                  onChange={(e) => e}
                  selected={new Date()}
                  placeholderText="Select Date"
                  className="w-full"
                />
              </div>

              <div className="relative">
                <Dropdown
                  label="Select Role"
                  options={['Sales Executive', 'Marketing Executive', 'All']}
                  value="Select Role"
                  fullWidth
                  onChange={(e) => e}
                />
              </div>

              <div className="relative">
                <Dropdown
                  label="Select Role"
                  options={['Sales', 'Marketing', 'All']}
                  value="Select Department"
                  fullWidth
                  onChange={(e) => e}
                />
              </div>

              <div className="relative">
                <Dropdown
                  label="Select Role"
                  options={['Approved', 'Reject', 'Pending', 'All']}
                  value="Approval status"
                  fullWidth
                  onChange={(e) => e}
                />
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left">
                    <button
                      onClick={toggleSelectAll}
                      className="flex items-center justify-center w-5 h-5 rounded border-2 border-teal-500 transition-all duration-200 hover:bg-teal-50"
                    >
                      {selectAll && (
                        <Check className="w-3 h-3 text-teal-600 transition-all duration-200" />
                      )}
                    </button>
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">Name</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">Role</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                    Department
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                    Expense type
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                    Submission Date
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                    Amount
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                    Current Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {expenses.map((expense, index) => (
                  <tr
                    key={expense.id}
                    className="hover:bg-gray-50 transition-colors duration-150"
                    style={{
                      animation: `fadeInUp 0.3s ease-out ${index * 0.05}s both`,
                    }}
                  >
                    <td className="px-6 py-4">
                      <button
                        onClick={() => toggleExpenseSelection(expense.id)}
                        className="flex items-center justify-center w-5 h-5 rounded border-2 border-teal-500 transition-all duration-200 hover:bg-teal-50"
                      >
                        {expense.selected && (
                          <Check className="w-3 h-3 text-teal-600 transition-all duration-200" />
                        )}
                      </button>
                    </td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">{expense.name}</td>
                    <td className="px-6 py-4 text-sm text-gray-600">{expense.role}</td>
                    <td className="px-6 py-4 text-sm text-gray-600">{expense.department}</td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center">
                          <FileText className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer transition-colors duration-200">
                          {expense.expenseType}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600">
                      {formatDate(expense.submissionDate)}
                    </td>
                    <td className="px-6 py-4 text-sm font-semibold text-gray-900">
                      {formatAmount(expense.amount)}
                    </td>
                    <td className="px-6 py-4">
                      <span className={getStatusBadge(expense.status)}>{expense.status}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default BatchExport;
