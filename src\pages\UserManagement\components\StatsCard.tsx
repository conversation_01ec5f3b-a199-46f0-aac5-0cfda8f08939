import type { ReactNode } from 'react';

interface StatsCardProps {
  title: string;
  value: string | number;
  change: string;
  changeColor: string;
  iconBg: string;
  iconColor: string;
  icon: ReactNode;
}

export default function StatsCard({
  title,
  value,
  change,
  changeColor,
  iconBg,
  iconColor,
  icon,
}: StatsCardProps) {
  return (
    <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-semibold sm:text-sm text-gray-500 mb-1">{title}</p>
          <p className="text-xl sm:text-2xl font-bold text-gray-700">{value}</p>
          <p className={`text-xs font-semibold sm:text-sm mt-1 ${changeColor}`}>{change}</p>
        </div>
        <div className={`${iconBg} p-2 sm:p-3 rounded-full`}>
          <div className={`w-5 h-5 sm:w-6 sm:h-6 ${iconColor}`}>{icon}</div>
        </div>
      </div>
    </div>
  );
}
