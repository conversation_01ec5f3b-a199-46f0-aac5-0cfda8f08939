import { useState } from 'react';
import { useNavigate } from 'react-router-dom';



const AddEmployeesOptions = () => {
  const [employeesAdded, setEmployeesAdded] = useState(2);
  const navigate = useNavigate();
  const handleAddEmployee = () => {
    navigate('/onboarding/add-employee/form');
    setEmployeesAdded((prev) => prev + 1);
  };

  const handleImportEmployee = () => {
    navigate('/onboarding/import-employees');
    setEmployeesAdded(prev => prev + 1);
  };

  const handleIntegrateAD = () => {
    navigate('/onboarding/ad-integration-settings');
    setEmployeesAdded((prev) => prev + 1);
  };
  return (
    <div className="min-h-screen ">
      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full" />
                </div>

                <div className="w-50 h-10">
                  <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full" />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em] ">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">Setup Progress</p>
              <p className="text-xs text-gray-500">2 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step <= 2 ? 'bg-[#06B217]' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <div className="w-5 h-5 bg-white rounded-full" />
          </div>
          <h1 className="text-3xl font-semibold text-gray-600">Add Employee</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="space-y-8">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Add Manually</h2>
                <p className="text-gray-600 mb-4">
                  Add employees one by one by entering their details manually
                </p>
                <button
                  onClick={handleAddEmployee}
                  className="px-6 py-3 bg-gradient-to-l from-[#203A43] to-[#2C5364] text-white font-medium rounded-full hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                  Add Manually
                </button>
                <p className="text-sm text-[#5C738A] mt-3 font-extrabold">
                  {employeesAdded} employees added
                </p>
              </div>
              <div className="flex-shrink-0 ml-8">
                <img src="/add-employee.png" className="w-64 h-40 object-contain" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-8 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Import from CSV</h2>
                <p className="text-gray-600 mb-4">
                  Add multiple employees at once by importing a CSV file
                </p>
                <button
                  onClick={handleImportEmployee}
                  className="px-6 py-3 bg-gradient-to-l from-[#203A43] to-[#2C5364] text-white font-medium rounded-full hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                  Import from CSV
                </button>
              </div>
              <div className="flex-shrink-0 ml-8">
                <img
                  src="/import-csv.png"
                  alt="Import from CSV Illustration"
                  className="w-64 h-40 object-contain"
                />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-8 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-800 mb-2">
                  Integrate Active Directory
                </h2>
                <p className="text-gray-600 mb-4">Sync with company directory</p>
                <button
                  onClick={handleIntegrateAD}
                  className="px-6 py-3 bg-gradient-to-l from-[#203A43] to-[#2C5364] text-white font-medium rounded-full hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                  Integrate
                </button>
              </div>
              <div className="flex-shrink-0 ml-8">
                <img
                  src="/add-directory.png"
                  alt="Active Directory Illustration"
                  className="w-64 h-40 object-contain"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-between pt-8">
            <button
              type="button"
              className="px-10 py-4 bg-gray-100 text-gray-700 font-medium rounded-xl hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors text-base"
            >
              Back
            </button>
            <button
              type="button"
              className="px-10 py-4 bg-teal-500 text-white font-medium rounded-xl hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base"
            >
              Next
            </button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AddEmployeesOptions;
