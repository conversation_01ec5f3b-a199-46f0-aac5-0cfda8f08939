import { useState } from 'react';
import type { ExpenseData, ApprovalData, DisputeData } from '../../../mockData/mockData';
import Pagination from '../../../components/Pagination';
import ApprovalTable from './ApprovalTable';
import DisputesTable from './DisputesTable';
import AllExpenseFiltersSection from './AllExpenseFiltersSection';
import ApprovalFiltersSection from './ApprovalFiltersSection';
import DisputeFiltersSection from './DisputeFiltersSection';
import AllExpenseTable from './AllExpenseTable';

interface TabContentProps {
  activeTab: string;
  tabCategory: string;
  expenseData?: ExpenseData[];
  approvalData?: ApprovalData[];
  disputeData?: DisputeData[];
}

const ITEMS_PER_PAGE = 5;

const TabContent = ({
  activeTab,
  tabCategory,
  expenseData,
  approvalData,
  disputeData,
}: TabContentProps) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  const getTabTitle = () => {
    switch (tabCategory) {
      case 'all':
        return 'All Expenses';
      case 'approvals':
        return 'Management Approvals';
      case 'disputes':
        return 'Raised Disputes';
      default:
        return 'All Expenses';
    }
  };

  const getCurrentData = () => {
    switch (tabCategory) {
      case 'all':
        return expenseData || [];
      case 'approvals':
        return approvalData || [];
      case 'disputes':
        return disputeData || [];
      default:
        return expenseData || [];
    }
  };

  const currentData = getCurrentData();
  const totalPages = Math.ceil(currentData.length / ITEMS_PER_PAGE);
  const paginatedData = currentData.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className={`${activeTab === tabCategory ? 'block' : 'hidden'} p-4 sm:p-6`}>
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <h2 className="text-lg font-semibold text-gray-900">{getTabTitle()}</h2>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="cursor-pointer bg-white text-gray-700 shadow-sm hover:bg-teal-50 px-6 py-3 rounded-full flex items-center justify-between gap-6 text-sm w-full sm:w-fit"
        >
          <span className="font-bold text-teal-500">Filters</span>
          <img src="/filterIcon.svg" alt="filter-icon" className="h-4" />
        </button>
      </div>

      {tabCategory === 'all' && showFilters && (
        <div className="mb-6">
          <AllExpenseFiltersSection />
        </div>
      )}

      {tabCategory === 'approvals' && showFilters && (
        <div className="mb-6">
          <ApprovalFiltersSection />
        </div>
      )}

      {tabCategory === 'disputes' && showFilters && (
        <div className="mb-6">
          <DisputeFiltersSection />
        </div>
      )}

      <div className="overflow-x-auto">
        {tabCategory === 'all' && <AllExpenseTable data={paginatedData as ExpenseData[]} />}

        {tabCategory === 'approvals' && <ApprovalTable data={paginatedData as ApprovalData[]} />}

        {tabCategory === 'disputes' && <DisputesTable data={paginatedData as DisputeData[]} />}
      </div>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      )}
    </div>
  );
};

export default TabContent;
