import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { Button } from '../../components/Button';
import Pagination from '../../components/Pagination';
import AddCategoryModal from './components/AddCategoryModal';
import { categoriesData } from '../../mockData/categoriesData';

interface Category {
  id: string;
  icon: string;
  iconBg: string;
  name: string;
  description: string;
  status: 'Active' | 'Inactive';
}

const ITEMS_PER_PAGE = 6;

const Categories = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [categories, setCategories] = useState<Category[]>(categoriesData);

  const handleAddCategory = (newCategory: { name: string; description: string; icon: string }) => {
    const category: Category = {
      id: (categories.length + 1).toString(),
      icon: newCategory.icon,
      iconBg: 'bg-teal-600',
      name: newCategory.name,
      description: newCategory.description,
      status: 'Active',
    };
    setCategories([...categories, category]);
  };

  const paginatedCategories = categories.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(categories.length / ITEMS_PER_PAGE);

  return (
    <div className="p-6 sm:p-8 bg-gray-50">
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">Categories</h1>
          <Breadcrumb pageName="Categories" />
        </div>
        <Button
          className="brand-gradient text-sm text-white py-2.5 px-5 rounded-md shadow-md"
          onClick={() => setIsModalOpen(true)}
        >
          + Add Category
        </Button>
      </div>

      <div className="bg-white rounded-2xl shadow-md">
        <div className="overflow-x-auto w-full">
          <table className="w-full min-w-[900px] divide-y divide-gray-200 text-sm">
            <thead className="">
              <tr className="text-nowrap">
                <th className="px-6 py-4 text-left font-medium text-gray-600">Icon</th>
                <th className="px-6 py-4 text-left font-medium text-gray-600">Category Name</th>
                <th className="px-6 py-4 text-left font-medium text-gray-600">Description</th>
                <th className="px-6 py-4 text-left font-medium text-gray-600">Status</th>
                <th className="px-6 py-4 text-left font-medium text-gray-600">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {paginatedCategories.map((category) => (
                <tr key={category.id} className="hover:bg-gray-50 text-nowrap">
                  <td className="px-6 py-4">
                    <img src={category.icon} alt={category.name} className="h-7" />
                  </td>
                  <td className="px-6 py-4 text-gray-900 font-medium">{category.name}</td>
                  <td className="px-6 py-4 text-gray-700 max-w-xs text-wrap">{category.description}</td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-block px-2 py-1 rounded-full text-xs font-semibold ${
                        category.status === 'Active'
                          ? 'bg-green-100 text-green-700'
                          : 'bg-gray-100 text-gray-700'
                      }`}
                    >
                      {category.status}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <button
                      className="text-gray-600 font-bold text-sm cursor-pointer"
                      onClick={() => alert(`Edit ${category.name}`)}
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="p-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={(page) => setCurrentPage(page)}
          />
        </div>
      </div>

      <AddCategoryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddCategory}
      />
    </div>
  );
};

export default Categories;
