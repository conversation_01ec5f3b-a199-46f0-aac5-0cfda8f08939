interface StatusBadgeProps {
  status: string;
  variant?: 'approval' | 'dispute' | 'general';
  className?: string;
}

const StatusBadge = ({ status, variant = 'approval', className = '' }: StatusBadgeProps) => {
  const getStatusStyles = () => {
    const baseStyles = 'px-4 py-1.5 rounded-full text-xs font-medium';
    
    switch (variant) {
      case 'approval':
        switch (status) {
          case 'Manager Approved':
          case 'Finance Approved':
          case 'Approved':
          case 'All Approved':
            return `${baseStyles} bg-teal-100 text-teal-800`;
          case 'Manager Rejected':
          case 'Rejected':
            return `${baseStyles} bg-red-100 text-red-800`;
          case 'In review':
          case 'Under Review':
            return `${baseStyles} bg-blue-100 text-blue-800`;
          case 'Pending':
          default:
            return `${baseStyles} bg-gray-100 text-gray-800`;
        }
      
      case 'dispute':
        switch (status) {
          case 'Resolved':
          case 'Approved':
            return `${baseStyles} bg-green-100 text-green-800`;
          case 'Open':
          case 'In Progress':
            return `${baseStyles} bg-blue-100 text-blue-800`;
          case 'Closed':
            return `${baseStyles} bg-gray-100 text-gray-800`;
          case 'Pending':
          default:
            return `${baseStyles} bg-yellow-100 text-yellow-800`;
        }
      
      case 'general':
      default:
        switch (status.toLowerCase()) {
          case 'active':
          case 'approved':
          case 'success':
            return `${baseStyles} bg-green-100 text-green-800`;
          case 'inactive':
          case 'rejected':
          case 'failed':
            return `${baseStyles} bg-red-100 text-red-800`;
          case 'pending':
          case 'in progress':
            return `${baseStyles} bg-yellow-100 text-yellow-800`;
          default:
            return `${baseStyles} bg-gray-100 text-gray-800`;
        }
    }
  };

  return (
    <span className={`${getStatusStyles()} ${className}`}>
      {status}
    </span>
  );
};

export default StatusBadge;