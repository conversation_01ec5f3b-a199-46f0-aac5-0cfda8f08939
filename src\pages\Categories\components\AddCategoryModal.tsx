import { useState } from 'react';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/Button';
import InputField from '../../../components/InputField';
import TextArea from '../../../components/TextArea';
import { Search } from 'lucide-react';

interface AddCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (category: { name: string; description: string; icon: string }) => void;
}

const AddCategoryModal = ({ isOpen, onClose, onSubmit }: AddCategoryModalProps) => {
  const [categoryName, setCategoryName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  const availableIcons = [
    { name: 'certificate', path: '/certificateIcon.svg', category: 'misc' },
    { name: 'pen', path: '/penIcon.svg', category: 'misc' },
    { name: 'misc', path: '/miscIcon.svg', category: 'misc' },
    { name: 'lodging', path: '/lodgingIcon.svg', category: 'misc' },
    { name: 'plane', path: '/planeIcon.svg', category: 'misc' },
    { name: 'burger', path: '/burgerCategoryIcon.svg', category: 'misc' },
  ];

  const filteredIcons = availableIcons.filter(
    (icon) =>
      icon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      icon.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (categoryName && description && selectedIcon) {
      onSubmit({
        name: categoryName,
        description,
        icon: selectedIcon,
      });

      setCategoryName('');
      setDescription('');
      setSelectedIcon('');
      setSearchTerm('');
      onClose();
    }
  };

  const getIconDisplayName = (iconPath: string) => {
    return iconPath.split('/').pop()?.replace('Icon.svg', '').replace('CategoryIcon.svg', '') || '';
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add New Category"
      className="max-w-4xl"
      disableBackdropClick
    >
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
          <div className="flex flex-col h-full">
            <div className="space-y-6 flex-1">
              <div>
                <InputField
                  label="Category Name"
                  value={categoryName}
                  onChange={setCategoryName}
                  placeholder="Enter category name"
                  required
                  size="sm"
                />
              </div>

              <div>
                <TextArea
                  label="Description"
                  value={description}
                  onChange={setDescription}
                  placeholder="Enter a brief description of this category"
                  rows={4}
                  required
                  size="sm"
                />
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 min-h-[120px] flex flex-col justify-center">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Selected Icon</h4>
                {selectedIcon ? (
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-white border border-gray-200 rounded-lg flex items-center justify-center shadow-sm">
                      <img
                        src={selectedIcon}
                        alt="Selected icon"
                        width={24}
                        height={24}
                        className="object-contain"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 capitalize">
                        {getIconDisplayName(selectedIcon)}
                      </p>
                      <p className="text-xs text-gray-500">{selectedIcon}</p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center text-gray-400">
                    <div className="text-center">
                      <div className="w-12 h-12 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <Search className="h-5 w-5" />
                      </div>
                      <p className="text-sm">No icon selected</p>
                      <p className="text-xs">Choose an icon from the right</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-col h-full">
            <div className="space-y-4 flex-1">
              <div>
                <InputField
                  label="Choose an Icon"
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder="Search icons by name or category..."
                  required
                  size="sm"
                  icon={<Search className="h-4 w-4" />}
                />
              </div>

              <div
                className="border border-gray-200 rounded-lg bg-gray-50 flex flex-col"
                style={{ height: '320px' }}
              >
                <div className="flex-1 overflow-y-auto p-4">
                  {filteredIcons.length > 0 ? (
                    <div className="grid grid-cols-6 gap-3">
                      {filteredIcons.map((icon) => (
                        <div
                          key={icon.name}
                          className={`
                            aspect-square border-2 rounded-lg p-3 flex items-center justify-center 
                            cursor-pointer transition-all duration-200 bg-white hover:shadow-md
                            ${
                              selectedIcon === icon.path
                                ? 'border-blue-500 ring-2 ring-blue-200 shadow-md'
                                : 'border-gray-200 hover:border-gray-300'
                            }
                          `}
                          onClick={() => setSelectedIcon(icon.path)}
                          title={`${icon.name} (${icon.category})`}
                        >
                          <img
                            src={icon.path}
                            alt={icon.name}
                            width={20}
                            height={20}
                            className="object-contain"
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10 text-gray-500 h-full flex flex-col justify-center items-center space-y-2">
                      <Search className="h-10 w-10 opacity-40" />
                      <p className="text-base font-medium">
                        No icons found matching “{searchTerm}”
                      </p>
                      <p className="text-sm text-gray-400">
                        Try searching by category or icon name
                      </p>
                    </div>
                  )}
                </div>

                <div className="border-t border-gray-200 px-4 py-2 bg-white rounded-b-lg">
                  <p className="text-xs text-gray-500">
                    {searchTerm
                      ? `${filteredIcons.length} icons found`
                      : `${availableIcons.length} icons available`}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500 font-semibold">
            <span className="text-red-500">*</span>All fields are required to create a category
          </p>
          <div className="flex space-x-3">
            <Button
              className="brand-gradient text-sm text-white py-3 px-12 rounded-md"
              onClick={() => alert('creating category...')}
            >
              Submit
            </Button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default AddCategoryModal;
