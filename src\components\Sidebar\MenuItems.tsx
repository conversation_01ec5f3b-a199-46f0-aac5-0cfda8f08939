import React from 'react';
import HorizontalWhiteDivider from '../HorizontalWhiteDivider';

interface MenuItem {
  path: string;
  label: string;
  icon: string;
}

interface MenuItemsProps {
  isCollapsed: boolean;
  currentPath: string;
  handleNavClick: (path: string) => void;
  handleLogout: () => void;
}

const menuItems: MenuItem[] = [
  { path: '/', label: 'Home', icon: '/HomeIcon.svg' },
  { path: '/users', label: 'User Management', icon: '/userManagementIcon.svg' },
  { path: '/expense-reports', label: 'Expense Reports', icon: '/expenseRepoersIcon.svg' },
  { path: '/policies', label: 'Policy Management', icon: '/policyManagementIcon.svg' },
  { path: '/categories', label: 'Categories', icon: '/categoriesIcon.svg' },
  { path: '/integrations', label: 'Integrations', icon: '/integrationIcon.svg' },
  { path: '/audit-trail', label: 'Audit Trail', icon: '/auditTrailIcon.svg' },
  { path: '/settings', label: 'Settings', icon: '/settingsIcon.svg' },
];

const MenuItems: React.FC<MenuItemsProps> = ({
  isCollapsed,
  currentPath,
  handleNavClick,
  handleLogout,
}) => {
  return (
    <nav className="px-4 py-6 space-y-0.5 text-sm text-gray-200">
      {menuItems.map((item) => {
        const isActive = currentPath === item.path;

        return (
          <div key={item.path} className="w-full">
            <button
              onClick={() => handleNavClick(item.path)}
              className={`w-full flex items-center ${
                isCollapsed ? 'justify-center px-2' : 'space-x-3 px-4'
              } py-3 rounded-lg hover:bg-white/10 transition-all duration-200 group relative ${
                isActive ? 'bg-white/10' : ''
              } cursor-pointer`}
              title={isCollapsed ? item.label : ''}
            >
              <img src={item.icon} alt={item.label} className="h-4 w-4 flex-shrink-0" />
              <span
                className={`font-medium transition-all duration-300 ${
                  isCollapsed ? 'opacity-0 scale-0 absolute' : 'opacity-100 scale-100'
                }`}
              >
                {item.label}
              </span>
              {isCollapsed && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-slate-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-50">
                  {item.label}
                </div>
              )}
            </button>

            {(item.label === 'Expense Reports' || item.label === 'Settings') && (
              <div className="mt-5 mb-2">
                <HorizontalWhiteDivider />
              </div>
            )}
          </div>
        );
      })}

      <button
        onClick={handleLogout}
        className={`w-full flex items-center ${
          isCollapsed ? 'justify-center px-2' : 'space-x-3 px-4'
        } py-3 rounded-lg text-gray-200 hover:bg-white/10 transition-all duration-200 group relative cursor-pointer`}
        title={isCollapsed ? 'Log Out' : ''}
      >
        <img src="/logoutIcon.svg" className="h-5 w-5 flex-shrink-0" alt="logout" />
        <span
          className={`font-medium transition-all duration-300 ${
            isCollapsed ? 'opacity-0 scale-0 absolute' : 'opacity-100 scale-100'
          }`}
        >
          Log Out
        </span>

        {isCollapsed && (
          <div className="absolute left-full ml-2 px-2 py-1 bg-slate-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-50">
            Log Out
          </div>
        )}
      </button>

      <div className="my-2">
        <HorizontalWhiteDivider />
      </div>
    </nav>
  );
};

export default MenuItems;
