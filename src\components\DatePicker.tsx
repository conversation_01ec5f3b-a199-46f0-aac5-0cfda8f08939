import { useState } from 'react';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { format } from 'date-fns';

interface DatePickerWithLabelProps {
  selected: Date | undefined;
  onChange: (date: Date | undefined) => void;
  placeholderText?: string;
  className?: string;
  dateFormat?: string;
}

const DatePickerWithLabel = ({
  selected,
  onChange,
  placeholderText = 'Date',
  className = '',
  dateFormat = 'dd/MM/yyyy',
}: DatePickerWithLabelProps) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const formatted = selected ? format(selected, dateFormat) : '';

  return (
    <div
      tabIndex={0}
      onBlur={(e) => {
        if (!e.currentTarget.contains(e.relatedTarget)) {
          setShowCalendar(false);
        }
      }}
      className="relative inline-block focus:outline-none"
    >
      <button
        type="button"
        onClick={() => setShowCalendar((prev) => !prev)}
        className={`cursor-pointer flex items-center justify-between gap-8 px-6 py-3 rounded-full bg-white shadow-sm transition text-sm ${className}`}
      >
        <span className={`text-gray-500 ${selected ? 'text-gray-700' : ''}`}>
          {selected ? formatted : placeholderText}
        </span>
        <img src="/calenderIcon.svg" alt="calender" className="h-4" />
      </button>

      {showCalendar && (
        <div className="absolute z-20 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg p-4">
          <DayPicker
            mode="single"
            selected={selected}
            onSelect={(date) => {
              onChange(date);
              setShowCalendar(false);
            }}
            modifiersClassNames={{
              selected: 'bg-teal-600 text-white',
              today: 'text-teal-700 font-semibold',
            }}
            className="font-sans text-sm"
          />
        </div>
      )}
    </div>
  );
};

export default DatePickerWithLabel;
