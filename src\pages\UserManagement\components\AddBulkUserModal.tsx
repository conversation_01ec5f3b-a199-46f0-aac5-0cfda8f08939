import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/Button';
import { downloadUserTemplate, bulkUploadUsers } from '../../../api/userManagementApis';

interface AddBulkUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (file: File) => void;
}

const AddBulkUserModal: React.FC<AddBulkUserModalProps> = ({ isOpen, onClose, onSubmit }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      setSelectedFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
    },
    maxFiles: 1,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedFile && !isUploading) {
      setIsUploading(true);
      try {
        await bulkUploadUsers(selectedFile);
        onSubmit(selectedFile);
        onClose();
        setSelectedFile(null);
        alert('Users uploaded successfully!');
      } catch (error) {
        console.error('Error uploading users:', error);
        alert('Failed to upload users. Please check your file format and try again.');
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleDownloadSample = async () => {
    try {
      const blob = await downloadUserTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'user_template.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading template:', error);
      alert('Failed to download template. Please try again.');
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add Bulk User"
      className="max-w-3xl"
      disableBackdropClick
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-gray-600">
          <p className="text-sm font-semibold">
            Import employee data from a CSV file. Ensure your file includes columns for employee
            name, email, department, and role. Download a sample CSV to see the required format.
          </p>
        </div>

        <div className="flex justify-end">
          <Button
            type="button"
            onClick={handleDownloadSample}
            className="brand-gradient text-white text-sm px-6 py-2 rounded-full flex items-center gap-2 my-3"
          >
            Download Sample CSV
          </Button>
        </div>

        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-10 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
          } ${selectedFile ? 'bg-green-50 border-green-300' : ''}`}
        >
          <input {...getInputProps()} />

          <div className="flex flex-col items-center justify-center space-y-5">
            <div>
              <p className="text-md font-bold text-gray-800">
                {selectedFile ? `Selected: ${selectedFile.name}` : 'Drag and drop a CSV file here'}
              </p>
              <p className="text-sm text-gray-600 mt-2">Or click to browse</p>
            </div>

            {!selectedFile && (
              <Button
                type="button"
                className="brand-gradient text-white text-sm px-6 py-2 rounded-full flex items-center gap-2 my-3"
              >
                Select .CSV File
              </Button>
            )}
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button
            type="submit"
            disabled={!selectedFile || isUploading}
            className="brand-gradient text-white px-24 py-3 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isUploading ? 'Uploading...' : 'Submit'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddBulkUserModal;
