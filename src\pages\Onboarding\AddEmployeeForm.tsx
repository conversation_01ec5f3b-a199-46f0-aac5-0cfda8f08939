import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

import { useNavigate } from 'react-router-dom';

type Permissions = {
  viewExpenses: boolean;
  submitExpenses: boolean;
};

type CustomPermissions = {
  approveExpenses: boolean;
  viewAllExpenses: boolean;
  editAllExpenses: boolean;
  manageUsers: boolean;
};

type FormData = {
  firstName: string;
  lastName: string;
  email: string;
  department: string;
  manager: string;
  employeeId: string;
  role: string;
  permissions: Permissions;
  customPermissions: CustomPermissions;
};

const AddEmployee = () => {
  const navigate = useNavigate();
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    department: '',
    manager: '',
    employeeId: '',
    role: '',
    permissions: {
      viewExpenses: false,
      submitExpenses: false,
    },
    customPermissions: {
      approveExpenses: false,
      viewAllExpenses: false,
      editAllExpenses: false,
      manageUsers: false,
    },
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const target = e.target as HTMLInputElement | HTMLSelectElement;
    const { name, value, type } = target;
    const checked = (target as HTMLInputElement).checked;

    if (name.includes('.')) {
      const [parent, child] = name.split('.') as [
        keyof Pick<FormData, 'permissions' | 'customPermissions'>,
        string
      ];
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value,
      }));
    }
  };

  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const roleValue = e.target.value;
    setSelectedRole(roleValue);
    setFormData((prev) => ({
      ...prev,
      role: roleValue,
    }));
  };

  const handleSubmit = async () => {
    try {
      console.log('Form data to be sent to API:', formData);

      alert('Employee added successfully!');
      navigate('/onboarding/logo-upload');
    } catch (error) {
      console.error('Error adding employee:', error);
      alert('Error adding employee. Please try again.');
    }
  };

  console.log('selectedRole', selectedRole);

  return (
    <div className="min-h-screen">
      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full" />
                </div>

                <div className="w-50 h-10">
                  <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full" />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em] ">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">Setup Progress</p>
              <p className="text-xs text-gray-500">2 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step <= 2 ? 'bg-[#06B217]' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <div className="w-5 h-5 bg-white rounded-full" />
          </div>
          <h1 className="text-3xl font-semibold text-gray-600">Add Employee</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="bg-white rounded-lg p-10">
          <div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div className="space-y-8">
                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    First Name
                  </label>
                  <input
                    name="firstName"
                    type="text"
                    placeholder="Enter first name"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                  />
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Last Name
                  </label>
                  <input
                    name="lastName"
                    type="text"
                    placeholder="Enter last name"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                  />
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Email Address
                  </label>
                  <input
                    name="email"
                    type="email"
                    placeholder="Enter email address"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                  />
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Department
                  </label>
                  <div className="relative">
                    <select
                      name="department"
                      value={formData.department}
                      onChange={handleInputChange}
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors appearance-none bg-white text-gray-500 text-base"
                    >
                      <option value="">Select department</option>
                      <option value="hr">Human Resources</option>
                      <option value="finance">Finance</option>
                      <option value="it">Information Technology</option>
                      <option value="marketing">Marketing</option>
                      <option value="sales">Sales</option>
                      <option value="operations">Operations</option>
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                  </div>
                </div>
              </div>

              <div className="space-y-8">
                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">Manager</label>
                  <div className="relative">
                    <select
                      name="manager"
                      value={formData.manager}
                      onChange={handleInputChange}
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors appearance-none bg-white text-gray-500 text-base"
                    >
                      <option value="">Select manager</option>
                      <option value="john-doe">John Doe</option>
                      <option value="jane-smith">Jane Smith</option>
                      <option value="mike-johnson">Mike Johnson</option>
                      <option value="sarah-wilson">Sarah Wilson</option>
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                  </div>
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">
                    Employee ID
                  </label>
                  <input
                    name="employeeId"
                    type="text"
                    placeholder="Enter employee ID"
                    value={formData.employeeId}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                  />
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-800 mb-3">Role</label>
                  <div className="relative">
                    <select
                      name="role"
                      value={formData.role}
                      onChange={handleRoleChange}
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors appearance-none bg-white text-gray-500 text-base"
                    >
                      <option value="">Select role</option>
                      <option value="admin">Admin</option>
                      <option value="manager">Manager</option>
                      <option value="employee">Employee</option>
                      <option value="accountant">Accountant</option>
                      <option value="hr">HR</option>
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                  </div>
                </div>
              </div>
            </div>

            {formData.role && (
              <div className="mt-12">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                  <div>
                    <h3 className="text-lg font-medium text-gray-800 mb-4">Role Permissions</h3>
                    <p className="text-sm text-gray-600 mb-6">
                      Default permissions for selected role will be applied. You can customize
                      permissions below.
                    </p>

                    <div className="space-y-4">
                      <label className="flex items-center space-x-3">
                        <input
                          name="permissions.viewExpenses"
                          type="checkbox"
                          checked={formData.permissions.viewExpenses}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">View Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="permissions.submitExpenses"
                          type="checkbox"
                          checked={formData.permissions.submitExpenses}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Submit Expenses</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-800 mb-4">
                      Customize Permissions (Optional)
                    </h3>

                    <div className="space-y-4">
                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.approveExpenses"
                          type="checkbox"
                          checked={formData.customPermissions.approveExpenses}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Approve Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.viewAllExpenses"
                          type="checkbox"
                          checked={formData.customPermissions.viewAllExpenses}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">View All Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.editAllExpenses"
                          type="checkbox"
                          checked={formData.customPermissions.editAllExpenses}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Edit All Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.manageUsers"
                          type="checkbox"
                          checked={formData.customPermissions.manageUsers}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Manage Users</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end mt-12">
              <button
                type="button"
                onClick={handleSubmit}
                className="px-10 py-4 bg-teal-500 text-white font-medium rounded-xl hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base"
              >
                Add Employee
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AddEmployee;
