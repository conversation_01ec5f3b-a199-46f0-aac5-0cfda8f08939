import { <PERSON><PERSON><PERSON>, <PERSON>, XAxi<PERSON>, ResponsiveContainer, Cell } from 'recharts';
import { useState, useEffect } from 'react';

const originalCategoryData = [
  { name: 'Meals & Entertainment', amount: 390 },
  { name: 'Travel & Transportation', amount: 1050 },
  { name: 'Professional Development', amount: 200 },
  { name: 'Office Expenses', amount: 50 },
  { name: 'Team Activities', amount: 150 },
  { name: 'Technology', amount: 100 },
  { name: 'Project Expenses', amount: 80 },
];

const getShortLabel = (label: string, isMobile: boolean) =>
  isMobile ? label.split(' ')[0] : label;

const renderMultilineTick =
  (isMobile: boolean) =>
  ({ x, y, payload }: any) => {
    const value = payload.value;

    const lines = value.includes(' & ')
      ? [value.split(' & ')[0] + ' &', value.split(' & ')[1]]
      : value.split(' ');

    const fontSize = isMobile ? 10 : 12;
    const lineHeight = isMobile ? 12 : 14;
    const xOffset = isMobile ? -30 : -20;

    return (
      <g transform={`translate(${x},${y + 10})`}>
        <text textAnchor="start" fill="#0f766e" fontSize={fontSize} fontWeight={600}>
          {lines.map((line: string, index: number) => (
            <tspan x={xOffset} dy={index === 0 ? 0 : lineHeight} key={index}>
              {line}
            </tspan>
          ))}
        </text>
      </g>
    );
  };

const CustomBar = (props: any) => {
  const { x, y, width, height, fill } = props;
  return (
    <g>
      <rect x={x} y={y} width={width} height={height} fill={fill} rx={4} ry={4} />
      <rect x={x} y={y} width={width} height={2} fill="#037878" />
    </g>
  );
};

const CategoryBreakdownChart = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const categoryData = originalCategoryData.map((item) => ({
    ...item,
    name: getShortLabel(item.name, isMobile),
  }));

  return (
    <div className="mb-4 sm:mb-6 lg:mb-8">
      <h2 className="text-base sm:text-lg font-bold text-gray-500 mb-4 sm:mb-6">
        Category Breakdown
      </h2>

      <div className="border border-gray-200 p-3 sm:p-4 lg:p-6 rounded-lg">
        <div className="mb-4 sm:mb-6">
          <p className="text-xs sm:text-sm font-semibold text-gray-700 mb-2">Expense Categories</p>
          <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-500">$4,320.00</p>
          <p className="flex flex-wrap items-center gap-1">
            <span className="font-semibold text-xs text-gray-500">Total</span>
            <span className="text-green-600 font-semibold text-xs">+10%</span>
          </p>
        </div>

        <div style={{ height: 240, width: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={categoryData}
              margin={{ top: 0, right: 10, left: 10, bottom: isMobile ? 70 : 60 }}
            >
              <XAxis
                dataKey="name"
                tick={renderMultilineTick(isMobile)}
                interval={0}
                axisLine={false}
                tickLine={false}
                height={isMobile ? 70 : 60}
              />
              <Bar dataKey="amount" shape={<CustomBar />}>
                {categoryData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill="#dafffc" />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default CategoryBreakdownChart;
