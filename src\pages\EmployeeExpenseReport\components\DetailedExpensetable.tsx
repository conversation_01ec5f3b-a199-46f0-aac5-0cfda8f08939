const expenseData = [
  {
    date: 'Jan 03 2025',
    expenseName: 'Client Dinner',
    category: 'Meals & Entertainment',
    amount: 120.0,
    status: 'Approved',
    notes: 'Dinner with ACME team',
  },
  {
    date: 'Jan 05 2025',
    expenseName: 'Travel to Conference',
    category: 'Travel & Transportation',
    amount: 500.0,
    status: 'Approved',
    notes: 'Flight to conference',
  },
  {
    date: 'Jan 07 2025',
    expenseName: 'Hotel Stay',
    category: 'Travel & Transportation',
    amount: 300.0,
    status: 'Approved',
    notes: 'Hotel for conference',
  },
  {
    date: 'Jan 08 2025',
    expenseName: 'Conference Fees',
    category: 'Professional Development',
    amount: 200.0,
    status: 'Approved',
    notes: 'Registration fee',
  },
  {
    date: 'Jan 10 2025',
    expenseName: 'Client Lunch',
    category: 'Meals & Entertainment',
    amount: 80.0,
    status: 'Approved',
    notes: 'Lunch with potential client',
  },
  {
    date: 'Jan 12 2025',
    expenseName: 'Office Supplies',
    category: 'Office Expenses',
    amount: 50.0,
    status: 'Approved',
    notes: 'Stationery and supplies',
  },
  {
    date: 'Jan 15 2025',
    expenseName: 'Client Dinner',
    category: 'Meals & Entertainment',
    amount: 120.0,
    status: 'In Review',
    notes: 'Need itemized receipt',
  },
  {
    date: 'Jan 18 2025',
    expenseName: 'Team Building Event',
    category: 'Team Activities',
    amount: 150.0,
    status: 'Approved',
    notes: 'Team outing',
  },
  {
    date: 'Jan 20 2025',
    expenseName: 'Software Subscription',
    category: 'Technology',
    amount: 100.0,
    status: 'Approved',
    notes: 'Annual subscription',
  },
  {
    date: 'Jan 22 2025',
    expenseName: 'Client Meeting',
    category: 'Meals & Entertainment',
    amount: 70.0,
    status: 'Approved',
    notes: 'Coffee meeting',
  },
  {
    date: 'Jan 25 2025',
    expenseName: 'Travel to Client Site',
    category: 'Travel & Transportation',
    amount: 250.0,
    status: 'Approved',
    notes: 'Train ticket',
  },
  {
    date: 'Jan 28 2025',
    expenseName: 'Project Materials',
    category: 'Project Expenses',
    amount: 80.0,
    status: 'Approved',
    notes: 'Materials for project',
  },
];

const DetailedExpenseTable = () => {
  return (
    <div>
      <h2 className="text-lg font-bold text-gray-500 mb-6">Detailed Expense Table</h2>

      <div className="overflow-x-auto rounded-xl border border-gray-200">
        <table className="w-full">
          <thead className="bg-white">
            <tr className="border-b border-gray-200 bg-gray-50">
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Date
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Expense Name
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Category
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Amount ($)
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Status
              </th>
              <th className="text-left py-3 px-6 text-sm text-gray-700 text-nowrap font-bold">
                Notes
              </th>
            </tr>
          </thead>
          <tbody>
            {expenseData.map((expense, index) => (
              <tr
                key={index}
                className="border-b border-gray-200 text-gray-500 font-semibold text-nowrap"
              >
                <td className="py-4 px-6 text-sm">{expense.date}</td>
                <td className="py-4 px-6 text-sm font-sm">{expense.expenseName}</td>
                <td className="py-4 px-6 text-sm">{expense.category}</td>
                <td className="py-4 px-6 text-sm font-sm">{expense.amount.toFixed(2)}</td>
                <td className="py-4 px-6">
                  <span className="px-3 py-1 text-xs font-sm rounded-full">{expense.status}</span>
                </td>
                <td className="py-4 px-6 text-sm">{expense.notes}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DetailedExpenseTable;
