import React from 'react';
import { Users, FileX, AlertCircle } from 'lucide-react';

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: 'users' | 'file' | 'alert';
  actionButton?: React.ReactNode;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon = 'users',
  actionButton,
  className = '',
}) => {
  const getIcon = () => {
    const iconProps = {
      size: 48,
      className: 'text-gray-400 mb-4',
    };

    switch (icon) {
      case 'file':
        return <FileX {...iconProps} />;
      case 'alert':
        return <AlertCircle {...iconProps} />;
      case 'users':
      default:
        return <Users {...iconProps} />;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center py-12 px-4 text-center ${className}`}>
      {getIcon()}
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-500 mb-6 max-w-md">{description}</p>
      {actionButton && <div>{actionButton}</div>}
    </div>
  );
};

export default EmptyState;
