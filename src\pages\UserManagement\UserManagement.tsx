import { Users, DollarSign, UserPlus, UserMinus } from 'lucide-react';
import { useState } from 'react';
import StatsCard from './components/StatsCard';
import HorizontalBarChart from '../../components/charts/HorizontalBarChart';
import { Button } from '../../components/Button';
import InsightsCard from './components/InsightsCard';
import UserTable from './components/UserTable';
import AddUserModal from './components/AddUserModal';
import AddBulkUserModal from './components/AddBulkUserModal';
import { dummyUserTableData, userRoleData } from '../../mockData/mockData';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';

const UserManagement = () => {
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isAddBulkUserModalOpen, setIsAddBulkUserModalOpen] = useState(false);

  const handleAddUser = (userData: any) => {
    console.log('New user data:', userData);
  };

  const handleAddBulkUser = (file: File) => {
    console.log('CSV file uploaded successfully:', file.name, file.size);
    // You can add additional logic here like refreshing the user table
    // or showing a success notification
  };

  return (
    <main className="p-6 bg-gray-50">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">User Management</h1>
          <Breadcrumb pageName="User Management" />
        </div>
        <div className="flex flex-col sm:flex-row gap-3 text-gray-200 font-semibold">
          <Button
            className="bg-teal-700 px-4 py-3 rounded-md text-sm"
            onClick={() => setIsAddBulkUserModalOpen(true)}
          >
            + Add Bulk User
          </Button>
          <Button
            className="brand-gradient px-4 py-3 rounded-md text-sm"
            onClick={() => setIsAddUserModalOpen(true)}
          >
            + Add User
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
        <StatsCard
          title="Total Active Users"
          value={250}
          change="+10%"
          changeColor="text-green-600"
          iconBg="bg-blue-100"
          iconColor="text-blue-600"
          icon={<Users />}
        />

        <StatsCard
          title="Average Expenses per User"
          value="$1,100"
          change="+8%"
          changeColor="text-green-600"
          iconBg="bg-green-100"
          iconColor="text-green-600"
          icon={<DollarSign />}
        />

        <StatsCard
          title="New Users This Month"
          value={75}
          change="-5%"
          changeColor="text-red-600"
          iconBg="bg-purple-100"
          iconColor="text-purple-600"
          icon={<UserPlus />}
        />

        <StatsCard
          title="Inactive Users"
          value={3}
          change="-2%"
          changeColor="text-red-600"
          iconBg="bg-red-100"
          iconColor="text-red-600"
          icon={<UserMinus />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6">
            Users by Role
          </h3>
          <div className="h-auto min-h-[280px]">
            <HorizontalBarChart
              data={userRoleData.map((item) => ({
                label: item.role,
                value: item.count,
              }))}
            />
          </div>
        </div>

        <InsightsCard />
      </div>

      <UserTable title="All Users" users={dummyUserTableData} />

      <AddUserModal
        isOpen={isAddUserModalOpen}
        onClose={() => setIsAddUserModalOpen(false)}
        onSubmit={handleAddUser}
      />

      <AddBulkUserModal
        isOpen={isAddBulkUserModalOpen}
        onClose={() => setIsAddBulkUserModalOpen(false)}
        onSubmit={handleAddBulkUser}
      />
    </main>
  );
};

export default UserManagement;
