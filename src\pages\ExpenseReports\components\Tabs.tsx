interface TabsProps {
  activeTab: string;
  onChange: (tab: 'all' | 'approvals' | 'disputes') => void;
}

const Tabs = ({ activeTab, onChange }: TabsProps) => {
  const tabs: { key: 'all' | 'approvals' | 'disputes'; label: string }[] = [
    { key: 'all', label: 'All Expenses' },
    { key: 'approvals', label: 'Approvals' },
    { key: 'disputes', label: 'Disputes' },
  ];

  return (
    <div className="p-4 sm:p-6 border-b border-gray-200">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 max-w-2xl mx-auto">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.key;

          return (
            <button
              key={tab.key}
              onClick={() => onChange(tab.key)}
              className={`cursor-pointer w-full text-sm px-3 py-2 rounded-md font-medium text-center transition-all duration-200 ease-in-out focus:outline-none ${
                isActive
                  ? 'bg-teal-600 text-white shadow-md ring-2'
                  : 'bg-gray-100 text-gray-700 hover:bg-teal-50 hover:text-teal-700 hover:shadow-sm hover:ring-teal-200'
              }`}
            >
              {tab.label}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default Tabs;
