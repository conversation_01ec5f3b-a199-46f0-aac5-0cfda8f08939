import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Edit, Upload } from 'lucide-react';
import ApprovalWorkflow from './components/ApprovalWorkflow';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import NotificationSettings from './components/NotificationSettings';
import EmployeeManagement from './components/EmployeeManagement';

interface OrganizationDetails {
  companyName: string;
  companyAddress: string;
  contactEmail: string;
  contactPhone: string;
  industry: string;
  fiscalYearStart: string;
}

interface RejectionReason {
  id: number;
  label: string;
  checked: boolean;
}

type TabId = 'general' | 'users' | 'approval' | 'notifications';
type EditingSection = keyof OrganizationDetails | 'all' | null;
type IndustryOption = 'Technology' | 'Finance' | 'Healthcare' | 'Manufacturing' | 'Retail';

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabId>('general');
  const [editingSection, setEditingSection] = useState<EditingSection>(null);
  const [companyLogo, setCompanyLogo] = useState<string>('/api/placeholder/150/150');

  const [orgDetails, setOrgDetails] = useState<OrganizationDetails>({
    companyName: 'Tech Solutions Inc.',
    companyAddress: '123 Innovation Drive, Tech City, CA 90210',
    contactEmail: '<EMAIL>',
    contactPhone: '(*************',
    industry: 'Technology',
    fiscalYearStart: 'January 1st',
  });

  const [rejectionReasons, setRejectionReasons] = useState<RejectionReason[]>([
    { id: 1, label: 'Late Submission', checked: true },
    { id: 2, label: 'Missing Receipt(s)', checked: true },
    { id: 3, label: 'Unclear Expense Description', checked: false },
    { id: 4, label: 'Incorrect/Invalid Receipt(s)', checked: true },
    { id: 5, label: 'Exceeds Policy Limit (Amount)', checked: false },
    { id: 6, label: 'Exceeds Policy Limit (Category)', checked: true },
    { id: 7, label: 'Inappropriate Expense Category', checked: false },
    { id: 8, label: 'Duplicate Expense', checked: true },
    { id: 9, label: 'Unreasonable Expense', checked: false },
    { id: 10, label: 'Requires Further Information', checked: true },
  ]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) {
        alert('File size must be less than 2MB');
        return;
      }

      if (!file.type.startsWith('image/')) {
        alert('Please upload an image file');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        if (e.target?.result) {
          setCompanyLogo(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    },
    maxFiles: 1,
    maxSize: 2 * 1024 * 1024, // 2MB
  });

  const handleOrgDetailsEdit = (field: EditingSection): void => {
    setEditingSection(field);
  };

  const handleOrgDetailsSave = (field: keyof OrganizationDetails, value: string): void => {
    setOrgDetails((prev) => ({ ...prev, [field]: value }));
    setEditingSection(null);
  };

  const handleRejectionReasonToggle = (id: number): void => {
    setRejectionReasons((prev) =>
      prev.map((reason) => (reason.id === id ? { ...reason, checked: !reason.checked } : reason))
    );
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    field: keyof OrganizationDetails,
    value: string
  ): void => {
    if (e.key === 'Enter') {
      handleOrgDetailsSave(field, value);
    }
  };

  const tabs: Array<{ id: TabId; label: string }> = [
    { id: 'general', label: 'General Settings' },
    { id: 'users', label: 'Users & Permissions' },
    { id: 'approval', label: 'Approval Workflow' },
    { id: 'notifications', label: 'Notifications' },
  ];

  const industryOptions: IndustryOption[] = [
    'Technology',
    'Finance',
    'Healthcare',
    'Manufacturing',
    'Retail',
  ];

  return (
    <>
      <div className="p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Settings</h1>
            <Breadcrumb pageName="Settings" />
          </div>
        </div>

        <div className="border-b border-slate-200 mb-8">
          <nav className="flex flex-wrap gap-4 rounded-md bg-slate-100 p-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`rounded-md px-5 py-2 text-sm font-semibold transition-colors ${
                  activeTab === tab.id
                    ? 'bg-teal-700 text-white shadow-sm'
                    : 'text-slate-600 hover:bg-slate-200'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {activeTab === 'general' && (
          <div className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-slate-800">Organization Details</h2>
                    <button
                      onClick={() => handleOrgDetailsEdit('all')}
                      className="text-teal-600 hover:text-teal-700 text-sm font-medium flex items-center gap-1"
                    >
                      <Edit size={14} />
                      Edit
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Company Name
                      </label>
                      {editingSection === 'companyName' ? (
                        <div className="flex gap-2">
                          <input
                            type="text"
                            defaultValue={orgDetails.companyName}
                            className="flex-1 px-3 py-2 border border-slate-300 rounded-md text-sm"
                            onBlur={(e) => handleOrgDetailsSave('companyName', e.target.value)}
                            onKeyDown={(e) =>
                              handleKeyDown(e, 'companyName', e.currentTarget.value)
                            }
                            autoFocus
                          />
                        </div>
                      ) : (
                        <p className="text-slate-800 font-medium">{orgDetails.companyName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Company Address
                      </label>
                      {editingSection === 'companyAddress' ? (
                        <textarea
                          defaultValue={orgDetails.companyAddress}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm"
                          rows={2}
                          onBlur={(e) => handleOrgDetailsSave('companyAddress', e.target.value)}
                          autoFocus
                        />
                      ) : (
                        <p className="text-slate-600 text-sm">{orgDetails.companyAddress}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Contact Email
                      </label>
                      {editingSection === 'contactEmail' ? (
                        <input
                          type="email"
                          defaultValue={orgDetails.contactEmail}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm"
                          onBlur={(e) => handleOrgDetailsSave('contactEmail', e.target.value)}
                          onKeyDown={(e) => handleKeyDown(e, 'contactEmail', e.currentTarget.value)}
                          autoFocus
                        />
                      ) : (
                        <p className="text-slate-600 text-sm">{orgDetails.contactEmail}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Contact Phone Number
                      </label>
                      {editingSection === 'contactPhone' ? (
                        <input
                          type="tel"
                          defaultValue={orgDetails.contactPhone}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm"
                          onBlur={(e) => handleOrgDetailsSave('contactPhone', e.target.value)}
                          onKeyDown={(e) => handleKeyDown(e, 'contactPhone', e.currentTarget.value)}
                          autoFocus
                        />
                      ) : (
                        <p className="text-slate-600 text-sm">{orgDetails.contactPhone}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Industry
                      </label>
                      {editingSection === 'industry' ? (
                        <select
                          defaultValue={orgDetails.industry}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm"
                          onChange={(e) => handleOrgDetailsSave('industry', e.target.value)}
                          autoFocus
                        >
                          {industryOptions.map((option) => (
                            <option key={option} value={option}>
                              {option}
                            </option>
                          ))}
                        </select>
                      ) : (
                        <p className="text-slate-600 text-sm">{orgDetails.industry}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Fiscal Year Start Date
                      </label>
                      {editingSection === 'fiscalYearStart' ? (
                        <input
                          type="text"
                          defaultValue={orgDetails.fiscalYearStart}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm"
                          onBlur={(e) => handleOrgDetailsSave('fiscalYearStart', e.target.value)}
                          onKeyDown={(e) =>
                            handleKeyDown(e, 'fiscalYearStart', e.currentTarget.value)
                          }
                          autoFocus
                        />
                      ) : (
                        <p className="text-slate-600 text-sm">{orgDetails.fiscalYearStart}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-slate-800">Company Logo</h2>
                    <button className="text-teal-600 hover:text-teal-700 text-sm font-medium flex items-center gap-1">
                      <Edit size={14} />
                      Edit
                    </button>
                  </div>

                  <div className="text-center">
                    <div
                      {...getRootProps()}
                      className={`w-32 h-32 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4 relative group cursor-pointer border-2 border-dashed transition-colors ${
                        isDragActive
                          ? 'border-teal-500 bg-teal-50'
                          : 'border-teal-200 hover:border-teal-300'
                      }`}
                    >
                      <input {...getInputProps()} />
                      <img
                        src={companyLogo}
                        alt="Company Logo"
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <Upload size={20} className="text-white" />
                      </div>
                    </div>
                    <p className="text-sm text-slate-600">
                      {isDragActive ? 'Drop the image here...' : 'Click or drag to upload new logo'}
                    </p>
                    <p className="text-xs text-slate-400 mt-1">Max file size: 2MB</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-slate-800">Content Management</h2>
                <button className="text-teal-600 hover:text-teal-700 text-sm font-medium">
                  + Add Rejection Reason
                </button>
              </div>

              <div className="mb-6">
                <h3 className="text-base font-medium text-slate-800 mb-4">Rejection reasons</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {rejectionReasons.map((reason) => (
                    <label key={reason.id} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={reason.checked}
                        onChange={() => handleRejectionReasonToggle(reason.id)}
                        className="w-4 h-4 text-teal-600 border-slate-300 rounded focus:ring-teal-500"
                      />
                      <span className="text-sm text-slate-700">{reason.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-slate-800">Invitation Email Content</h2>
                <button className="text-teal-600 hover:text-teal-700 text-sm font-medium flex items-center gap-1">
                  <Edit size={14} />
                  Edit
                </button>
              </div>

              <div>
                <label
                  htmlFor="invitationEmail"
                  className="block text-sm font-medium text-slate-700 mb-2"
                >
                  Email Message
                </label>
                <textarea
                  id="invitationEmail"
                  name="invitationEmail"
                  rows={6}
                  placeholder="Write your invitation email content here..."
                  className="w-full border border-slate-300 rounded-lg shadow-sm p-4 text-sm resize-y focus:outline-none focus:ring-2 focus:ring-teal-500"
                />
              </div>
            </div>
          </div>
        )}

        {activeTab !== 'general' && (
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
            <h3 className="text-lg font-medium text-slate-800 mb-2">
              {activeTab === 'users' && <EmployeeManagement />}
              {activeTab === 'approval' && <ApprovalWorkflow />}
              {activeTab === 'notifications' && <NotificationSettings />}
            </h3>
          </div>
        )}
      </div>
    </>
  );
};

export default Settings;
