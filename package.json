{"name": "expenso", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-router": "^1.125.6", "axios": "^1.11.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "papaparse": "^5.5.3", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "recharts": "^3.1.0", "tailgrids": "^2.3.0", "tailwindcss": "^4.1.11", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.3"}}