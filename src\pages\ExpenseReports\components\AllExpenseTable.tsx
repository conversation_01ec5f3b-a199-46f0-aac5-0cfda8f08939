import { useNavigate } from 'react-router-dom';
import type { ExpenseData } from '../../../mockData/mockData';
import StatusBadge from '../../../components/StatusBadge';

interface AllExpenseTableProps {
  data: ExpenseData[];
}

const AllExpenseTable = ({ data }: AllExpenseTableProps) => {
  const navigate = useNavigate();

  const navigateToEmployeeDetails = (employeeId: string) => {
    navigate(`/expense-reports/${employeeId}`);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="overflow-x-auto">
        <table className="w-full min-w-[720px] mt-4">
          <thead>
            <tr className="border-b border-gray-200 text-left text-sm text-gray-800">
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Employee Name</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Department</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Submission Date</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Total Amount</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Approval Status</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Payment Method</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.length > 0 ? (
              data.map((expense) => (
                <tr key={expense.id} className="border-b border-gray-100 hover:bg-gray-50 text-nowrap">
                  <td className="py-4 px-2 sm:px-4">
                    <div
                      className="text-sm font-medium text-nowrap text-gray-600 cursor-pointer"
                      onClick={() => navigateToEmployeeDetails(expense.id)}
                    >
                      {expense.employeeName}
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">{expense.department}</td>
                  <td className="py-4 px-2 sm:px-4 text-xs font-semibold text-gray-600">
                    {expense.submissionDate}
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm font-medium text-gray-600">
                    ${expense.totalAmount.toFixed(2)}
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <StatusBadge status={expense.approvalStatus} variant="approval" />
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">
                    {expense.paymentMethod}
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <button
                      onClick={() => alert('Mark action')}
                      className="text-xs text-nowrap font-bold text-gray-500 hover:text-gray-800 cursor-pointer"
                    >
                      Mark Action
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr className='text-nowrap'>
                <td colSpan={7} className="py-8 px-2 sm:px-4 text-center text-gray-500">
                  No expenses found for this category
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AllExpenseTable;
