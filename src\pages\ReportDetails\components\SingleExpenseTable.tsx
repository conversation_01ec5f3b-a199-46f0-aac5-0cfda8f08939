import { useState } from 'react';
import Pagination from '../../../components/Pagination';

interface Expense {
  id: string;
  type: string;
  description: string;
  date: string;
  amount: string;
  status: 'Check Done' | 'Rejected';
  approval: 'Approved' | 'Rejected';
}

interface ExpenseTableProps {
  title: string;
  expenses: Expense[];
}

const ITEMS_PER_PAGE = 5;

export default function SingleExpenseTable({ title, expenses }: ExpenseTableProps) {
  const [approvalFilter] = useState('');
  const [statusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const filteredExpenses = expenses.filter((expense) => {
    return (
      (approvalFilter === '' || expense.approval === approvalFilter) &&
      (statusFilter === '' || expense.status === statusFilter)
    );
  });

  const paginatedExpenses = filteredExpenses.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(filteredExpenses.length / ITEMS_PER_PAGE);

  return (
    <div className="bg-white rounded-lg shadow-sm mt-6">
      <div className="p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 gap-4">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900">{title}</h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full min-w-[720px] mt-4">
            <thead>
              <tr className="border-b border-gray-200 text-left text-sm text-gray-800">
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Expense Type</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Submission Date</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Total Amount</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Status</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Approval Status</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedExpenses.map((expense) => (
                <tr key={expense.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-2 sm:px-4">
                    <div className="flex items-start gap-2">
                      <img src="/categoryIcon.svg" alt="Category Icon" />
                      <div className="text-sm font-medium text-nowrap text-gray-500">
                        {expense.type}
                      </div>
                    </div>
                  </td>

                  <td className="py-4 px-2 sm:px-4 text-xs font-semibold text-gray-600">
                    {expense.date}
                  </td>

                  <td className="py-4 px-2 sm:px-4 text-sm font-medium text-gray-600">
                    {expense.amount}
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <span className="text-nowrap bg-gray-100 text-gray-700 px-2.5 py-1 text-xs rounded-full">
                      {expense.status}
                    </span>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <span
                      className={`px-2.5 py-1 text-xs rounded-full text-white 
                        ${expense.approval === 'Approved' ? 'bg-green-500' : 'bg-red-500'}`}
                    >
                      {expense.approval}
                    </span>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <button className="text-xs text-nowrap font-bold text-gray-500 hover:text-gray-800">
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  );
}
