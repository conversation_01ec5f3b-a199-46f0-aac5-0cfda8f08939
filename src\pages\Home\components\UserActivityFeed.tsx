import React from 'react';
import { userActivity } from '../../../mockData/homeMockData';

const UserActivityFeed: React.FC = () => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 min-h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">User Activity</h3>
      <div className="relative flex-grow">
        <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200 transform -translate-x-1/2"></div>

        <div className="space-y-8">
          {userActivity.map((activity, index) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="relative flex items-start">
                <div className="relative z-10">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 border-8 border-white">
                    <Icon className="h-4 w-4" />
                  </div>

                  {index < userActivity.length - 1 && (
                    <div className="absolute top-12 left-1/2 -translate-x-1/2 h-full w-0.5 bg-gray-200 z-0" />
                  )}
                </div>

                <div className="ml-4 pt-1">
                  <p className="text-sm font-medium text-gray-900 leading-snug">{activity.title}</p>
                  <p className="text-xs text-gray-500 mt-0.5">{activity.subtitle}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default UserActivityFeed;
