import React from 'react';
import { FileText, Search, CheckCircle } from 'lucide-react';

const activities = [
  {
    id: 1,
    title: 'Report Submitted',
    date: '2024-03-15',
    icon: FileText,
    bg: 'bg-teal-100',
    iconColor: 'text-teal-600',
  },
  {
    id: 2,
    title: 'Report Reviewed',
    date: '2024-03-16',
    icon: Search,
    bg: 'bg-blue-100',
    iconColor: 'text-blue-600',
  },
  {
    id: 3,
    title: 'Report Approved By Manager',
    date: '2024-03-16',
    icon: CheckCircle,
    bg: 'bg-green-100',
    iconColor: 'text-green-600',
  },
  {
    id: 4,
    title: 'Report Approved By Finance',
    date: '2024-03-16',
    icon: CheckCircle,
    bg: 'bg-green-100',
    iconColor: 'text-green-600',
  },
  {
    id: 5,
    title: 'Report Approved',
    date: '2024-03-17',
    icon: CheckCircle,
    bg: 'bg-green-100',
    iconColor: 'text-green-600',
  },
];

const ActivityInsightsFeed: React.FC = () => {
  return (
    <div className="bg-gray-50 rounded-lg p-6 border border-gray-100 h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">User Activity</h3>

      <div className="relative flex-grow pr-2">
        <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200 transform -translate-x-1/2"></div>

        <div className="space-y-8">
          {activities.map((activity, index) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="relative flex items-start">
                <div className="relative z-10">
                  <div
                    className={`w-12 h-12 ${activity.bg} rounded-full flex items-center justify-center border-6 border-white`}
                  >
                    <Icon className={`h-4 w-4 ${activity.iconColor}`} />
                  </div>

                  {index < activities.length - 1 && (
                    <div className="absolute top-12 left-1/2 -translate-x-1/2 h-full w-0.5 bg-gray-200 z-0" />
                  )}
                </div>

                <div className="ml-4 pt-1">
                  <p className="text-sm font-medium text-gray-900 leading-snug">{activity.title}</p>
                  <p className="text-xs text-gray-500 mt-0.5">{activity.date}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ActivityInsightsFeed;
